{"name": "aigc-profiler", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "format": "prettier --write src/", "build:test": "run-p type-check \"build-only {@}\" --", "build:online": "run-p type-check \"build-only {@}\" --", "lint:style": "stylelint src/**/*.{vue,less,css} --fix", "prepare": "husky"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"src/**/*.{js,cjs,mjs,jsx,ts,tsx,vue}": ["prettier --write", "eslint --cache --fix"], "src/**/*.{less,css,vue}": ["prettier --write", "stylelint --fix"]}, "browserslist": ["> 0.25%", "last 2 versions", "ie > 8"], "dependencies": {"@types/papaparse": "^5.3.16", "@vueuse/core": "^13.2.0", "element-plus": "^2.9.10", "file-saver": "^2.0.5", "papaparse": "^5.5.3", "vue": "^3.5.12", "vue-router": "^4.4.5"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@tsconfig/node20": "^20.1.4", "@types/file-saver": "^2.0.7", "@types/node": "^20.16.11", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.1.4", "@vue/eslint-config-prettier": "^10.0.0", "@vue/eslint-config-typescript": "^14.0.1", "@vue/tsconfig": "^0.5.1", "eslint": "^9.12.0", "eslint-plugin-vue": "^9.29.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "npm-run-all2": "^6.2.3", "postcss": "^8.4.47", "postcss-html": "^1.7.0", "postcss-less": "^6.0.0", "postcss-preset-env": "^10.0.7", "prettier": "^3.3.3", "stylelint": "^16.10.0", "stylelint-config-recommended-less": "^3.0.1", "stylelint-config-standard": "^36.0.1", "stylelint-less": "^3.0.1", "stylelint-order": "^6.0.4", "stylelint-selector-bem-pattern": "^4.0.1", "terser": "^5.39.0", "typescript": "~5.5.4", "unplugin-auto-import": "^19.2.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.5", "vite-plugin-vue-devtools": "^7.4.6", "vue-tsc": "^2.1.6"}}