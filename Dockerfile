FROM image-docker.zuoyebang.cc/base/node:18-slim as builder

# prod
WORKDIR /output
WORKDIR /home/<USER>/
COPY . /home/<USER>/
RUN npm install --registry=https://ued.zuoyebang.cc/npm/
ARG CI_FE_DEBUG
ARG REPO_GIT_REMOTE_ADDRESS
ARG CI_COMMIT_SHA
RUN if [ "$CI_FE_DEBUG" = "true" ] ; then pnpm run build:test ; else pnpm run build:online ; fi
 
# 运行
FROM image-docker.zuoyebang.cc/privbase/fe-nginx:1.2.9
 
ARG APP_NAME
ENV APP_NAME $APP_NAME
ARG REPO_NAME
ENV REPO_NAME $REPO_NAME
# 仅用于通用的前端，部分前端视情况来组织目录结构
COPY --from=builder /home/<USER>/dist/ /home/<USER>/www/static/$APP_NAME/
