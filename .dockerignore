# IntelliJ project files
.idea
*.iml
out
gen
.git*
*.md
*/*/.git
*.sh
output
Dockerfile
.gitlab-ci.yml

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
.DS_Store
dist
dist-ssr
coverage
*.local

/cypress/videos/
/cypress/screenshots/

# Editor directories and files
.vscode/*
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

*.tsbuildinfo


.pnpm-store
pnpm-lock.yaml
.editorconfig
.dockerignore
.husky
.huskyrc
# 忽略 ESLint 缓存文件
.eslintcache

vite.config.ts.timestamp*
