<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2024-10-22 15:58:18
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-05-23 17:53:54
 * @Description:
-->
<script setup lang="ts">
import { ref } from 'vue';
import ActionPanel from './components/action-panel/action-panel.vue';
import dataTable from './components/data-table/index';

const fileData = ref<string[]>([]);
const loading = ref(false);
const onSearch = () => {
  loading.value = true;
};
const onSearchComplete = (data: string[] | undefined) => {
  loading.value = false;
  if (data) {
    fileData.value = data;
  }
};
</script>

<template>
  <el-container>
    <el-main class="home-view__main">
      <action-panel
        @search="onSearch"
        @search-complete="onSearchComplete"
      ></action-panel>
      <data-table :data="fileData" :loading="loading"></data-table>
    </el-main>
  </el-container>
</template>

<style>
.home-view__main {
  margin: 0 auto;
  max-width: 768px;
}
</style>
