<!--
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-21 11:49:41
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-05-26 13:32:29
 * @Description:
-->
<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import {
  genFileId,
  type UploadFile,
  type UploadInstance,
  type UploadRawFile,
  type UploadUserFile
} from 'element-plus';

const emits = defineEmits<{
  change: [file: File | undefined];
}>();

const upload = useTemplateRef<UploadInstance>('upload');
const fileArray = ref<UploadUserFile[]>([]);
watch(fileArray, (newVal) => {
  emits('change', newVal[0]?.raw);
});
const onExceed = (files: File[]) => {
  upload.value?.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  upload.value?.handleStart(file);
};

const handleInvalidFile = (errorMessage: string) => {
  upload.value?.clearFiles();
  if (fileArray.value.length > 0) {
    upload.value?.handleStart(fileArray.value[0].raw as UploadRawFile);
  }

  ElMessage({
    type: 'error',
    message: errorMessage
  });
};

const onChange = (uploadFile: UploadFile) => {
  if (uploadFile.status !== 'ready' || !uploadFile.raw) {
    return;
  }

  if (uploadFile.raw.type !== 'text/csv') {
    handleInvalidFile('请上传 csv 格式的文件');

    return;
  }

  const MAX_SIZE = 1024 * 1024 * 100;

  if (uploadFile.raw.size > MAX_SIZE) {
    handleInvalidFile('请上传小于 100MB 的文件');

    return;
  }
};
</script>

<template>
  <el-upload
    ref="upload"
    v-model:file-list="fileArray"
    :limit="1"
    :auto-upload="false"
    accept=".csv"
    :on-exceed="onExceed"
    :on-change="onChange"
  >
    <template #trigger
      ><el-button color="#4e70f2">上传表格</el-button></template
    >
    <i class="csv-uploader__icon-tips" v-show="fileArray.length > 0"
      ><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
        <path
          fill="currentColor"
          d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336z"
        ></path>
      </svg>
    </i>
  </el-upload>
</template>

<style>
.csv-uploader__icon-tips {
  display: inline-block;
  margin-left: 10px;
  width: 30px;
  height: 30px;
  vertical-align: top;
  color: #67c23a;
}
</style>
