<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2025-05-22 14:23:12
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-22 15:49:21
 * @Description:
-->
<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus';
import { ref, useTemplateRef } from 'vue';
import { useThrottleFn } from '@vueuse/core';

const emits = defineEmits<{
  search: [uid: string];
}>();

interface FormData {
  uid: string;
}
const formData = ref<FormData>({ uid: '' });

const formRef = useTemplateRef<FormInstance>('formRef');
const rules: FormRules<typeof formData.value> = {
  uid: [
    {
      validator(rule, value, callback) {
        if (value.length === 0) {
          callback();
          return;
        }

        if (/^\d+$/.test(value)) {
          callback();
        } else {
          callback('请输入数字');
        }
      },
      trigger: 'blur'
    }
  ]
};

const search = useThrottleFn(() => {
  if (!formRef.value) {
    return;
  }

  if (formData.value.uid.length === 0) {
    return;
  }

  formRef.value.validate((isValid) => {
    if (isValid) {
      emits('search', formData.value.uid);
    }
  });
}, 1000);
</script>

<template>
  <el-form
    class="uid-form"
    ref="formRef"
    :model="formData"
    :rules="rules"
    hide-required-asterisk
    inline
  >
    <el-form-item label="请输入 uid：" prop="uid">
      <el-input class="uid-form__input" v-model="formData.uid" clearable />
    </el-form-item>
    <el-form-item class="uid-form__btn-search">
      <el-button color="#4e70f2" @click="search">查找</el-button>
    </el-form-item>
  </el-form>
</template>

<style>
.uid-form__input {
  width: 150px;
}

.uid-form .uid-form__btn-search {
  margin-right: 0;
}
</style>
