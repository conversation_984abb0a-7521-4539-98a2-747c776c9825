<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2025-05-21 11:23:38
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-23 18:07:25
 * @Description:
-->
<script setup lang="ts">
import { shallowRef } from 'vue';
import CsvUploader from './components/csv-uploader/index';
import UidForm from './components/uid-form/index';
import getDataFromCsv from './helpers/get-data-from-csv';

const emits = defineEmits<{
  search: [];
  'search-complete': [data: string[] | undefined];
}>();
const csvFile = shallowRef<File>();

const onCsvUploaderChange = (file: File | undefined) => {
  csvFile.value = file;
};

let isSearching = false;
const onSearch = async (uid: string) => {
  if (isSearching) {
    return;
  }

  if (!csvFile.value) {
    ElMessage({
      type: 'error',
      message: '未上传文件'
    });
    return;
  }

  isSearching = true;

  emits('search');

  const data = await getDataFromCsv(csvFile.value, uid);

  isSearching = false;

  if (!data) {
    ElMessage({
      type: 'error',
      message: '文件解析失败'
    });
  } else if (data.length === 0) {
    ElMessage({
      type: 'warning',
      message: '没有找到该用户数据'
    });
  }
  emits('search-complete', data);
};
</script>

<template>
  <div>
    <csv-uploader
      class="action-panel__csv-uploader"
      @change="onCsvUploaderChange"
    ></csv-uploader>
    <div class="action-panel__form">
      <uid-form @search="onSearch"></uid-form>
    </div>
  </div>
</template>

<style>
.action-panel__csv-uploader {
  display: inline-block;
  min-width: 180px;
}

.action-panel__form {
  float: right;
}
</style>
