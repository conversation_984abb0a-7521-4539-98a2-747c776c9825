/*
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2025-05-22 16:00:31
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-23 15:10:54
 * @Description:
 */
import <PERSON> from 'papaparse';

async function getDataFromCsv(file: File, uid: string) {
  const data: string[] = [];
  try {
    await new Promise<void>((resolve, reject) => {
      Papa.parse(file, {
        skipEmptyLines: true,
        chunk: (results) => {
          const chunkData = results.data.filter((item) => {
            if ((item as string[])[1] !== uid) {
              return false;
            }

            const reg = /^\{"text":"".*?"pid":""\}$/;

            if (
              reg.test((item as string[])[3]) &&
              (item as string[])[5].length === 0 &&
              (item as string[])[6].length === 0
            ) {
              return false;
            }
            return true;
          });

          data.push(...(chunkData as string[]));
        },
        complete: () => {
          resolve();
        },
        error: reject
      });
    });
  } catch {
    return;
  }

  return data;
}

export default getDataFromCsv;
