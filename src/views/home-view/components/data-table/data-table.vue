<!--
 * @Author: han<PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2025-05-21 11:28:51
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-05-23 17:40:48
 * @Description:
-->
<script setup lang="ts">
import { watch, ref } from 'vue';
import convertStrToTable from './helpers/convert-str-to-table';
import type { TableRowData } from './types';
import mergeImgs from './helpers/merge-imgs';
import downloadImg from './helpers/download-img';

const props = defineProps({
  data: {
    required: true,
    type: Array as () => string[]
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const pageCount = ref(1);
const currentPage = ref(1);
const pageSize = 20;

watch(currentPage, () => {
  updatePage();
});

let originalData: string[];
watch(
  () => {
    return props.data;
  },
  (newVal) => {
    originalData = newVal;
    pageCount.value = Math.ceil(newVal.length / pageSize);
    currentPage.value = 1;
    isMergeImgLoading.value = {};
    updatePage();
  }
);

const updatePage = () => {
  const startIndex = (currentPage.value - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  tableData.value = convertStrToTable(originalData.slice(startIndex, endIndex));
};

const getRoleColor = (role: 'user' | 'assistant' | 'system') => {
  return role === 'user'
    ? '#d9ecff'
    : role === 'assistant'
      ? '#e0f3d8'
      : '#faedd8';
};

const isMergeImgLoading = ref<Record<string, boolean>>({});
const isBtnMergeDisabled = ref(false);

const onMergeImgs = async (index: number, imgs: string[], texts: string[]) => {
  isMergeImgLoading.value[index] = true;
  isBtnMergeDisabled.value = true;
  const data = await mergeImgs(imgs, texts);
  if (data) {
    downloadImg(data);
  }

  isMergeImgLoading.value[index] = false;
  isBtnMergeDisabled.value = false;

  ElMessage({
    type: 'success',
    message: '合成成功，正在下载'
  });
};

const tableData = ref<TableRowData[]>([]);
</script>

<template>
  <div>
    <el-table :data="tableData" border v-loading="props.loading">
      <el-table-column label="用户文本">
        <template #default="scope">
          <el-tag
            v-if="scope.row.role.length > 0"
            :color="getRoleColor(scope.row.role)"
          >
            {{ scope.row.role }}：
          </el-tag>

          <p class="data-table__msg-text" v-if="scope.row.msgText.length > 0">
            {{ scope.row.msgText }}
          </p>
          <el-image
            :src="scope.row.msgPid"
            v-if="scope.row.msgPid.length > 0"
            :preview-src-list="[scope.row.msgPid]"
            preview-teleported
            alt=""
            lazy
          />
          <p
            class="data-table__intros"
            v-for="(item, index) in scope.row.intros"
            :key="index"
          >
            {{ item }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="图片或绘本">
        <template #default="scope">
          <el-image
            :src="pid"
            alt=""
            v-for="(pid, index) in scope.row.pids"
            :key="index"
            :preview-src-list="scope.row.pids"
            :initial-index="index"
            preview-teleported
            lazy
          />
          <el-button
            class="data-table__btn-merge"
            v-if="scope.row.pids.length > 1"
            @click="onMergeImgs(scope.$index, scope.row.pids, scope.row.intros)"
            :loading="isMergeImgLoading[scope.$index]"
            :disabled="isBtnMergeDisabled"
            >合成</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="时间">
        <template #default="scope">
          <span>{{ scope.row.createTime }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      class="data-table__pagination"
      v-model:current-page="currentPage"
      background
      layout="prev, pager, next"
      :page-count="pageCount"
      hide-on-single-page
    />
  </div>
</template>

<style>
.data-table__msg-text,
.data-table__intros {
  white-space: pre-line;
}

.data-table__pagination {
  float: right;
  margin-top: 10px;
}

.data-table__btn-merge {
  width: 100%;
}
</style>
