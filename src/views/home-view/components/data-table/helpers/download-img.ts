/*
 * @Author: ha<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-27 17:09:51
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-23 16:51:24
 * @Description: 截图页面并下载
 */

import { saveAs } from 'file-saver';

/**
 * 将 base64 转换为 Blob
 *
 * @param {string} base64 base64 字符串
 * @return {Blob} Blob 对象
 */
function base64ToBlob(base64: string) {
  const byteCharacters = window.atob(base64.split(',')[1]);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);

  return new Blob([byteArray], { type: 'image/png' });
}

/**
 * 截图页面并下载
 * @param filename 文件名
 */
function downloadImg(dataUrl: string, filename: string = 'merge') {
  saveAs(base64ToBlob(dataUrl), `${filename}.png`);
}

export default downloadImg;
