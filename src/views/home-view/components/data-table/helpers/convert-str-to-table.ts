/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-22 18:43:42
 * @LastEditors: hanlinqi <EMAIL>
 * @LastEditTime: 2025-05-23 15:53:04
 * @Description:
 */
import formatTimestamp from '../utils/format-timestamp';
import type { TableRowData } from '../types';

function convertStrToTable(content: string[]) {
  console.log(
    '🚀 > convert-str-to-table.ts:13 > convertStrToTable > content:',
    content
  );

  const tableData: TableRowData[] = [];

  if (content.length < 1) {
    return tableData;
  }

  content.sort(function (a, b) {
    const result = +a[4] - +b[4];
    if (result === 0) {
      if (a[0].length == 0) {
        return +a[7] - +b[7];
      }
      return +a[0] - +b[0];
    }

    return result;
  });

  const textPattern = /"text":"([^"]+)"/;
  const pidPattern = /"pid":"([^"]+)"/;
  const introsPattern = /[\[\]"]/g;

  for (const line of content) {
    const [, , role, msg, createTime, pids, intros] = line;
    const msgTextMatch = msg.match(textPattern);
    const msgPidMatch = msg.match(pidPattern);

    const introsArr = intros.replace(introsPattern, '').split(',');
    const pidsArr = pids.replace(introsPattern, '').split(',');

    tableData.push({
      role,
      msgText: msgTextMatch
        ? msgTextMatch[1].replace(/\\n/g, '\n').replace(/\\r/g, '\r')
        : '',
      msgPid: msgPidMatch
        ? 'https://deer-chat.cdnjtzy.com/' + msgPidMatch[1]
        : '',
      intros:
        introsArr.length === 1 && introsArr[0].length === 0 ? [] : introsArr,
      pids: pidsArr.length === 1 && pidsArr[0].length === 0 ? [] : pidsArr,
      createTime: formatTimestamp(
        +(createTime.length > 0 ? createTime + '000' : '')
      )
    });
  }

  return tableData;
}

export default convertStrToTable;
