/*
 * @Author: ha<PERSON><PERSON><PERSON> han<PERSON><EMAIL>
 * @Date: 2025-05-23 16:34:44
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-23 17:10:56
 * @Description:
 */
async function loadImage(src: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous';
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = src;
  });
}

function calculateCanvasSize(images: HTMLImageElement[]) {
  let totalWidth = 0;
  let totalHeight = 0;

  totalWidth = Math.max(...images.map((img) => img.width));
  totalHeight = images.reduce((sum, img) => {
    return sum + img.height;
  }, 0);

  return [totalWidth, totalHeight];
}

function sliceText(text: string, length: number) {
  if (!text) return [];

  const result = [];
  let remaining = text;

  while (remaining.length > 0) {
    const sliceLength = Math.min(length, remaining.length);

    // 提取当前片段
    const currentSlice = remaining.slice(0, sliceLength);
    result.push(currentSlice);

    // 更新剩余文本
    remaining = remaining.slice(sliceLength);
  }

  return result;
}

function drawText(
  ctx: CanvasRenderingContext2D,
  text: string,
  x: number,
  y: number
) {
  ctx.font = '36px Arial';
  ctx.fillStyle = '#ffffff';
  ctx.textAlign = 'left';
  ctx.fillText(text, x, y);
}

async function mergeImgs(imgs: string[], texts: string[]) {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    return;
  }

  try {
    const images = await Promise.all(imgs.map((src) => loadImage(src)));

    const [width, height] = calculateCanvasSize(images);
    canvas.width = width;
    canvas.height = height;

    const currentX = 0;
    let currentY = 0;

    images.forEach((img, index) => {
      ctx.drawImage(img, currentX, currentY);

      const text = texts[index];
      if (text) {
        const textSlice = sliceText(text, 10);
        const x = width * 0.1;
        const offsetY = 50;
        const y = currentY + img.height * 0.8 - textSlice.length * offsetY;

        textSlice.forEach((item, index) => {
          drawText(ctx, item, x, y + index * offsetY);
        });
      }

      currentY += img.height;
    });

    return canvas.toDataURL('image/png');
  } catch {
    return;
  }
}

export default mergeImgs;
