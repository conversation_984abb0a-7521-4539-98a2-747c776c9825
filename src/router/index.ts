/*
 * @Author: han<PERSON><PERSON> <EMAIL>
 * @Date: 2024-10-22 15:58:18
 * @LastEditors: han<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-21 11:00:04
 * @Description:
 */
import { createRouter, createWebHistory } from 'vue-router';
import HomeView from '../views/home-view/index';

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    }
  ]
});

export default router;
